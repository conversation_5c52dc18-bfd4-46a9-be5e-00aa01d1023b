import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:solar_icons/solar_icons.dart';

class Notifications extends StatelessWidget {
  const Notifications({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Using hardcoded English notification strings since they are dynamic content from server
    final List<Map<String, String>> notifications = [
      {
        'title': 'Your leave request has been approved',
        'message': 'Enjoy your time off',
        'time': 'Today, 08:00 AM',
      },
      {
        'title': 'Salary has been deposited',
        'message': 'Check your account for details',
        'time': 'Yesterday, 08:00 AM',
      },
      {
        'title': 'Official Holiday Tomorrow',
        'message': 'The company will be closed',
        'time': '01/12/2024',
      },
      {
        'title': 'Your shift starts in 30 minutes',
        'message': 'Don\'t forget to clock in',
        'time': '01/12/2024',
      },
      {
        'title': 'You are 10 minutes late',
        'message': 'Please clock in as soon as possible',
        'time': '01/12/2024',
      },
      {
        'title': 'Congratulations',
        'message': 'You\'ve been selected as Employee of the Month',
        'time': '01/12/2024',
      },
      {
        'title': 'Congratulations',
        'message': 'You\'ve been selected as Employee of the Month',
        'time': '01/12/2024',
      },
    ];

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        border: Border.all(
                          color: colors.strokeColor,
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    localization.notification,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Notifications list
            Expanded(
              child: ListView.separated(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                itemCount: notifications.length,
                separatorBuilder: (context, index) => _buildDivider(context),
                itemBuilder: (context, index) {
                  final notification = notifications[index];
                  return _buildNotificationItem(
                    context,
                    title: notification['title']!,
                    message: notification['message']!,
                    time: notification['time']!,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context, {
    required String title,
    required String message,
    required String time,
  }) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Padding(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bell icon with rounded container background
          Container(
            margin: const EdgeInsetsDirectional.only(top: 4),
            decoration: BoxDecoration(
              color: colors.primaryVariant,
              shape: BoxShape.circle,
            ),
            padding: const EdgeInsetsDirectional.all(10),
            child: SvgPicture.asset(
              'assets/icons/home_screen/notification-bing.svg',
              // width: 22,
              // height: 22,
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsetsDirectional.only(start: 11),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.fromSTEB(0, 12, 0, 0),
                    child: Text(
                      message,
                      style: textStyles.body2.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.fromSTEB(0, 10, 0, 0),
                    child: Text(
                      time,
                      style: textStyles.body3.copyWith(
                        color: colors.tertiaryText,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Divider
  Widget _buildDivider(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    return Divider(
      color: colors.strokeColor,
      thickness: 1,
    );
  }
}

import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/styles/util.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:solar_icons/solar_icons.dart';

class NotePopup extends StatelessWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final TextEditingController? controller;

  const NotePopup({
    super.key,
    this.onCancel,
    this.onSave,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // Use constraints instead of fixed height
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.4,
      ),
      padding: const EdgeInsetsDirectional.fromSTEB(8, 16, 8, 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 10, 10, 10),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                localization.addNote,
                style: textStyles.headline3.copyWith(
                  color: colors.primary,
                  fontSize: 16,
                ),
              ),
            ),
          ),

          // text field area
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsetsDirectional.symmetric(
                  horizontal: 10, vertical: 8),
              decoration: BoxDecoration(
                color: colors.surface,
                border: Border.all(
                  color: colors.strokeColor,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: controller,
                maxLines: null,
                expands: true,
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                ),
                decoration: InputDecoration(
                  hintText: localization.enterYourNoteHere,
                  hintStyle: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsetsDirectional.all(16),
                ),
              ),
            ),
          ),

          // Buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 16),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 44,
                    margin: const EdgeInsetsDirectional.only(end: 8),
                    child: OutlinedButton(
                      onPressed: onCancel ?? () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        backgroundColor: colors.surface,
                        foregroundColor: colors.tertiaryText,
                        side: BorderSide(
                          color: colors.tertiaryText,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.cancel.toLowerCase(),
                        style: textStyles.button.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Container(
                    height: 44,
                    margin: const EdgeInsetsDirectional.only(start: 8),
                    child: ElevatedButton(
                      onPressed: onSave ?? () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        foregroundColor: colors.primaryText,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.save,
                        style: textStyles.buttonMedium.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

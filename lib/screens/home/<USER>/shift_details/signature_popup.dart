import 'dart:typed_data';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:signature/signature.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class SignaturePopup extends StatefulWidget {
  final VoidCallback? onCancel;
  final Function(Uint8List)? onSave;

  const SignaturePopup({
    super.key,
    this.onCancel,
    this.onSave,
  });
  @override
  State<SignaturePopup> createState() => _SignaturePopupState();
}

class _SignaturePopupState extends State<SignaturePopup> {
  late SignatureController _controller;

  @override
  void initState() {
    super.initState();
    _controller = SignatureController(
      penStrokeWidth: 3,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final oldPoints = _controller.points;
    _controller.dispose();
    _controller = SignatureController(
      penStrokeWidth: 3,
      penColor: colors(context).onSurface,
      exportBackgroundColor: colors(context).surface,
      points: oldPoints, // Preserve any existing signature points
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    if (_controller.isEmpty) {
      final localization = AppLocalizations.of(context)!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localization.pleaseDrawSignature)),
      );
      return;
    }
    final data = await _controller.toPngBytes();
    if (data != null) {
      widget.onSave?.call(data);
      if (context.mounted) Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      height: 400,
      padding: const EdgeInsetsDirectional.fromSTEB(8, 16, 8, 16),
      child: Column(
        children: [
          // Title
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.drawSignature,
                  style: textStyles.headline4.copyWith(
                    color: colors.primary,
                    fontSize: 16,
                  ),
                ),
                // Clear button
                // IconButton(
                //   onPressed: () => _controller.clear(),
                //   icon: Icon(
                //     Icons.refresh,
                //     color: colors.primary,
                //   ),
                //   tooltip: 'Clear Signature',
                // ),
              ],
            ),
          ),

          // Signature area
          Expanded(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
              decoration: BoxDecoration(
                color: colors.surface,
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Signature(
                  controller: _controller,
                  backgroundColor: colors.surface,
                  width: double.infinity,
                  height: double.infinity,
                ),
              ),
            ),
          ),

          // Buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 16),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 44,
                    margin: const EdgeInsetsDirectional.only(end: 8),
                    child: OutlinedButton(
                      onPressed:
                          widget.onCancel ?? () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        backgroundColor: colors.surface,
                        foregroundColor: colors.tertiaryText,
                        side: BorderSide(
                          color: colors.tertiaryText,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.cancel.toLowerCase(),
                        style: textStyles.button.copyWith(
                          color: colors.secondaryText,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Container(
                    height: 44,
                    margin: const EdgeInsetsDirectional.only(start: 8),
                    child: ElevatedButton(
                      onPressed: _handleSave,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        foregroundColor: colors.primaryText,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.save,
                        style: textStyles.buttonMedium.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

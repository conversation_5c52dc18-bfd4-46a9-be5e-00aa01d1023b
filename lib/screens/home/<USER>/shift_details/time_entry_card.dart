import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/components/time_picker/rounded_time_picker.dart';
// import 'package:time_picker_with_timezone/time_picker_with_timezone.dart';

class TimeEntryCard extends StatelessWidget {
  final String title;
  final String time;
  final String location;
  final VoidCallback? onEdit;
  final Function(String)? onTimeChanged;

  const TimeEntryCard({
    super.key,
    required this.title,
    required this.time,
    required this.location,
    this.onEdit,
    this.onTimeChanged,
  });

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width: screenWidth - 32, // Fill (328px)
      height: 100,
      margin: const EdgeInsetsDirectional.only(bottom: 8),
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(width: 1, color: colors.primaryVariant),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with title and edit icon
          Container(
            padding: const EdgeInsetsDirectional.only(
              start: 12,
              top: 8,
              end: 12,
              bottom: 4, // Gap
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    final selectedTime = await showRoundedTimePicker(
                      context,
                      helpText: 'Checked Out',
                    );
                    if (selectedTime != null && onTimeChanged != null) {
                      final formattedTime = _formatTime(selectedTime, context);
                      onTimeChanged!(formattedTime);
                    }
                  },
                  child: Container(
                    child: Icon(
                      SolarIconsOutline.pen2,
                      color: colors.primary,
                      size: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Time
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 4,
              top: 4,
            ),
            child: Text(
              time,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
                height: 1.2,
              ),
            ),
          ),

          Container(
            margin: const EdgeInsets.only(top: 6),
          ),

          // Location
          Container(
            padding: const EdgeInsets.only(
              left: 12,
              right: 12,
              bottom: 8,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Location icon
                Container(
                  padding: const EdgeInsetsDirectional.all(4),
                  decoration: BoxDecoration(
                    color: colors.primaryVariant,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    SolarIconsOutline.mapPoint,
                    color: colors.primary,
                    size: 16,
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 6),
                ),
                Expanded(
                  child: Text(
                    location,
                    style: textStyles.body3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(TimeOfDay time, BuildContext context) {
    // Retrieve localization based on context
    final localization = AppLocalizations.of(context)!;

    final hour = time.hourOfPeriod == 0 ? 12 : time.hourOfPeriod;
    final minute = time.minute.toString().padLeft(2, '0');
    final period =
        time.period == DayPeriod.am ? localization.am : localization.pm;
    return '$hour:$minute $period';
  }
}

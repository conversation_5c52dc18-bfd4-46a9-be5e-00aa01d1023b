import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:ako_basma/styles/theme.dart';

/// AI Welcome Container component
class AIWelcomeContainer extends StatelessWidget {
  const AIWelcomeContainer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(80, 4, 80, 4),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10.73),
        child: BackdropFilter(
          filter: ImageFilter.blur(
              sigmaX: 20, sigmaY: 20), // Background blur effect
          child: Container(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
            decoration: BoxDecoration(
              // Blue gradient background for blur effect
              gradient: const LinearGradient(
                colors: [
                  Color(0x806D6D6D), // Top color
                  Color(0x80A6E0F2), // Bottom color
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius: BorderRadius.circular(10.73),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Welcome message
                Text(
                  localization.aiGreetingText,
                  style: textStyles.body2.copyWith(
                    color:
                        colors.primaryText, // Use primary text color insteads
                  ),
                  textAlign: TextAlign.center,
                ),

                // Spacing between title and capabilities
                Container(margin: const EdgeInsetsDirectional.only(top: 16)),

                // AI capabilities list
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildCapabilityText(
                      localization.aiCapablityLine1,
                      textStyles,
                      colors,
                    ),
                    Container(margin: const EdgeInsetsDirectional.only(top: 6)),
                    _buildCapabilityText(
                      localization.aiCapablityLine2,
                      textStyles,
                      colors,
                    ),
                    Container(margin: const EdgeInsetsDirectional.only(top: 6)),
                    _buildCapabilityText(
                      localization.aiCapablityLine3,
                      textStyles,
                      colors,
                    ),
                    Container(margin: const EdgeInsetsDirectional.only(top: 6)),
                    _buildCapabilityText(
                      localization.aiCapablityLine4,
                      textStyles,
                      colors,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Helper method to build capability text with consistent styling
  Widget _buildCapabilityText(
    String text,
    TextStyles textStyles,
    AppColors colors,
  ) {
    return Text(
      text,
      style: textStyles.body3.copyWith(
        color: colors.secondaryText,
        fontSize: 10,
      ),
      textAlign: TextAlign.center,
    );
  }
}

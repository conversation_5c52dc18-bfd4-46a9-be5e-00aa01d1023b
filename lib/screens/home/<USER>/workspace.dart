import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/company_news.dart';
import 'package:ako_basma/screens/home/<USER>/components/request_expenses.dart';
import 'package:ako_basma/screens/home/<USER>/components/request_leave_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/salary_popup.dart';
import 'package:ako_basma/screens/home/<USER>/components/schedule.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/timesheet_popup.dart';
import 'package:ako_basma/screens/home/<USER>/my_tasks.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'workspace_cards.dart';

class Workspace extends StatefulWidget {
  const Workspace({super.key});

  @override
  State<Workspace> createState() => _WorkspaceState();
}

class _WorkspaceState extends State<Workspace> {
  /// Shows success snackbar from the top when expense request is submitted
  void _showTopSnackbar(BuildContext context) {
    final localization = AppLocalizations.of(context)!;
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: localization.requestSubmittedSuccessfully,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/request_leave.svg',
                  width: 24,
                  height: 24,
                ),
                label: localization.requestForLeave,
                onTap: () {
                  showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    backgroundColor: Colors.transparent,
                    useRootNavigator: true,
                    builder: (context) => const RequestLeavePopup(),
                  );
                },
              ),
              // gap of 8px
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/Vector.svg',
                  width: 24,
                  height: 24,
                ),
                label: localization.timesheet,
                onTap: () {
                  showAdaptivePopup(
                    context,
                    (ctx, sc) => const TimesheetPopup(),
                    isDismissible: false,
                    scrollable: true,
                    contentPadding: EdgeInsets.zero,
                    topRadius: 0,
                    fullScreen: true,
                    useRootNavigator: true,
                  );
                },
              ),
            ],
          ),
          Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/schedule.svg',
                  width: 24,
                  height: 24,
                ),
                label: localization.schedule,
                onTap: () {
                  showAdaptivePopup(
                    context,
                    (ctx, sc) => const Schedule(),
                    isDismissible: false,
                    scrollable: true,
                    contentPadding: EdgeInsets.zero,
                    topRadius: 0,
                    fullScreen: true,
                    useRootNavigator: true,
                  );
                },
              ),
              buildWorkspaceCard(
                context,
                icon: SvgPicture.asset(
                  'assets/icons/workspace_screen/money.svg',
                  width: 24,
                  height: 24,
                ),
                label: localization.salary,
                onTap: () {
                  showAdaptivePopup(
                    context,
                    (ctx, sc) => const SalaryPopup(),
                    isDismissible: false,
                    scrollable: true,
                    contentPadding: EdgeInsets.zero,
                    topRadius: 0,
                    fullScreen: true,
                    useRootNavigator: true,
                  );
                },
              ),
            ],
          ),
          GestureDetector(
            onTap: () {
              showAdaptivePopup(
                context,
                (ctx, sc) => RequestExpenses(
                  onCancel: () => Navigator.pop(ctx),
                  onSuccess: () => _showTopSnackbar(
                      context), // Show success snackbar when submitted
                ),
                isDismissible: true,
                // scrollable: true,  /// this was causing the issue of keyboard not showing up
                contentPadding: EdgeInsets.zero,
                initialChildSize: 0.62, // Half of the default 0.9
                minChildSize: 0.4, // Half of the default 0.7
                useRootNavigator: true,
              );
            },
            child: Container(
              width: MediaQuery.of(context).size.width - 32,
              height: 70,
              margin: const EdgeInsetsDirectional.fromSTEB(8, 6, 8, 8),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
              ),
              padding: const EdgeInsetsDirectional.fromSTEB(8, 8, 8, 8),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/icons/workspace_screen/money.svg',
                    width: 24,
                    height: 24,
                  ),
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: Text(
                      localization.requestForExpenses,
                      textAlign: TextAlign.center,
                      style: textStyles.body3.copyWith(
                        color: colors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const MyTasks(),
          const CompanyNews(),
        ],
      ),
    );
  }
}

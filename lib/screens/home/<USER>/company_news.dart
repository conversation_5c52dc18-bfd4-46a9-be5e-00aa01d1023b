import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/util/ui/popups.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:ako_basma/screens/home/<USER>/components/company_news_card.dart';

class CompanyNews extends StatefulWidget {
  const CompanyNews({super.key});

  @override
  State<CompanyNews> createState() => _CompanyNewsState();
}

class _CompanyNewsState extends State<CompanyNews> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    // 4 company news images
    final items = List<String>.filled(4, 'assets/images/news.png');

    return Container(
      padding: const EdgeInsetsDirectional.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header aligned with workspace cards using an 8px horizontal margin
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.symmetric(horizontal: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.companyNews,
                  style: textStyles.headline4.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                GestureDetector(
                  onTap: () => _showAllCompanyNews(context),
                  child: Text(
                    localization.showAll,
                    style: textStyles.body3.copyWith(
                      color: colors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // Company news carousel with scroll indicator
          // CarouselSlider in try-catch to handle loading errors
          Builder(
            builder: (context) {
              try {
                return Stack(
                  children: [
                    CarouselSlider(
                      items: items.asMap().entries.map((entry) {
                        final index = entry.key;
                        final path = entry.value;
                        return GestureDetector(
                          onTap: () => _showAllCompanyNews(context),
                          child: CompanyNewsCard(
                            imagePath: path,
                            margin: EdgeInsetsDirectional.only(
                              start: index == 0 ? 8.0 : 0.0,
                              end: 12.0,
                              top: 4.0,
                              bottom: 4.0,
                            ),
                          ),
                        );
                      }).toList(),
                      options: CarouselOptions(
                        height: 225,
                        viewportFraction: 0.93, // viewport fraction is 90%
                        enlargeCenterPage: false, // Maintain consistent sizing
                        enableInfiniteScroll: false,
                        autoPlay: items.length >
                            1, // Only autoplay if more than 1 item
                        autoPlayInterval: const Duration(seconds: 3),
                        padEnds: false, // Removed default padding
                        scrollPhysics: items.length > 1
                            ? const BouncingScrollPhysics()
                            : const NeverScrollableScrollPhysics(), // Prevent scrolling if only 1 item
                        // onPageChanged to update current index
                        onPageChanged: (index, reason) {
                          setState(() {
                            _currentIndex = index;
                          });
                        },
                      ),
                    ),
                    // Scroll indicator positioned on the carousel
                    // show indicator if there are multiple slides
                    if (items.length > 1)
                      PositionedDirectional(
                        bottom: 16,
                        start: 16,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.onPrimary.withOpacity(
                                0.2), // ask tushar sir about the exact color
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: AnimatedSmoothIndicator(
                            activeIndex: _currentIndex,
                            count: items.length,
                            effect: SlideEffect(
                              dotHeight: 6,
                              dotWidth: 6,
                              spacing: 6,
                              activeDotColor: colors.primaryText,
                              dotColor: colors.tertiaryText,
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              } catch (e) {
                // Fallback UI in case CarouselSlider fails to load
                return Container(
                  height: 205,
                  margin: const EdgeInsetsDirectional.symmetric(horizontal: 8),
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: items.isNotEmpty
                        ? Image.asset(
                            items.first,
                            fit: BoxFit.cover,
                            width: double.infinity,
                            height: double.infinity,
                          )
                        : Center(
                            child: Icon(
                              Icons.image,
                              color: colors.tertiaryText,
                              size: 48,
                            ),
                          ),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  void _showAllCompanyNews(BuildContext context) {
    // Using root navigator to bypass bottom navigation bar for true full screen experience
    Navigator.of(context, rootNavigator: true).push(
      MaterialPageRoute(
        builder: (context) => AllCompanyNewsScreen(
          onBack: () {
            // Safe navigation check before popping
            if (Navigator.canPop(context)) {
              Navigator.of(context, rootNavigator: true).pop();
            }
          },
        ),
      ),
    );
  }
}

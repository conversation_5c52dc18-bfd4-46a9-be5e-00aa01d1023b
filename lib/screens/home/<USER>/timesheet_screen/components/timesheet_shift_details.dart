import 'dart:typed_data';
import 'package:ako_basma/screens/home/<USER>/shift_details/time_entry_card.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/components/button/primary_button.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:remixicon/remixicon.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/signature_popup.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/note_popup.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class TimesheetShiftDetails extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? resetToClockIn;

  const TimesheetShiftDetails({super.key, this.onBack, this.resetToClockIn});

  @override
  State<TimesheetShiftDetails> createState() => _TimesheetShiftDetailsState();
}

class _TimesheetShiftDetailsState extends State<TimesheetShiftDetails> {
  // state variables for time entry card which will be changed later
  String _clockInTime = '8:00 AM';
  String _clockOutTime = '3:30 PM';
  String _breakTime = '2:30 PM - 3:00';
  Uint8List? _signatureData;
  bool _hasSignature = false;
  // note state
  final TextEditingController _noteController = TextEditingController();
  bool _hasNote = false;

  // draw sign pop-up screen
  void _showSignaturePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => SignaturePopup(
        onCancel: () => Navigator.pop(context),
        onSave: (data) {
          // Update signature state - SignaturePopup handles its own navigation
          setState(() {
            _signatureData = data;
            _hasSignature = true;
          });
          _showChangesSavedSnackbar();
          // Removed Navigator.pop(context) to prevent double navigation
        },
      ),
    );
  }

  // add note pop-up screen
  void _showNotePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsetsDirectional.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: NotePopup(
          onCancel: () => Navigator.pop(context),
          controller: _noteController,
          onSave: () {
            // Update note state and close the popup
            setState(() {
              _hasNote = _noteController.text.trim().isNotEmpty;
            });
            Navigator.pop(context);
            _showChangesSavedSnackbar();
          },
        ),
      ),
    );
  }

  // show snackbar for signature and note changes saved
  void _showChangesSavedSnackbar() {
    final localization = AppLocalizations.of(context)!;
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: localization.changesSavedSuccessfully,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    Future.delayed(const Duration(seconds: 2), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  // top snackbar from top
  void _showTopSnackbar(BuildContext context) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    final localization = AppLocalizations.of(context)!;

    overlayEntry = OverlayEntry(
      builder: (context) => TopSnackbarWidget(
        message: localization.requestSentSuccessfully,
        onDismiss: () => overlayEntry.remove(),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }

  @override
  void dispose() {
    _noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final mediaQuery = MediaQuery.of(context);
    final screenWidth = mediaQuery.size.width;
    final localization = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Container(
          width: screenWidth,
          constraints: BoxConstraints(
            minHeight: mediaQuery.size.height,
          ),
          decoration: BoxDecoration(
            color: colors.background,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              // Header with back button and title
              Container(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (widget.onBack != null) {
                          widget.onBack!();
                        } else {
                          Navigator.of(context).pop();
                        }
                      },
                      child: Container(
                        padding:
                            const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                        decoration: BoxDecoration(
                          color: colors.backgroundContainer,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colors.strokeColor,
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          DirectionHelpers.getBackArrowIcon(context),
                          color: colors.primaryText,
                          size: 32,
                        ),
                      ),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(start: 12),
                    ),
                    Text(
                      localization.shiftDetails,
                      style: textStyles.headline3.copyWith(
                        color: colors.secondaryText,
                      ),
                    ),
                  ],
                ),
              ),

              // Main content - scrollable
              Expanded(
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  child: Container(
                    padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Container(
                          margin: const EdgeInsetsDirectional.only(top: 8),
                        ),

                        // Total Hours Container - displays total worked time
                        Container(
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              16, 12, 16, 12),
                          decoration: BoxDecoration(
                            color: colors.backgroundContainer,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: colors.primaryVariant,
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              // Left side - Clock icon and Total Hours text
                              Row(
                                children: [
                                  Icon(
                                    SolarIconsOutline.clockCircle,
                                    color: colors.secondaryText,
                                    size: 16,
                                  ),
                                  Container(
                                    margin: const EdgeInsetsDirectional.only(
                                        start: 4),
                                  ),
                                  Text(
                                    localization.totalHours,
                                    style: textStyles.body3.copyWith(
                                      color: colors.secondaryText,
                                    ),
                                  ),
                                ],
                              ),
                              // Right side - Time display
                              Text(
                                '10:30:00',
                                style: textStyles.headline.copyWith(
                                  color: colors.primaryText,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 10),

                        // Two new containers when time is edited
                        Row(
                          children: [
                            // Draw Signature Container
                            Expanded(
                              child: GestureDetector(
                                onTap: _showSignaturePopup,
                                child: Container(
                                  height: 72,
                                  padding: const EdgeInsetsDirectional.all(8),
                                  decoration: BoxDecoration(
                                    color: colors.backgroundContainer,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: colors.primaryVariant,
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Stack(
                                        alignment: Alignment.center,
                                        children: [
                                          Icon(
                                            _hasSignature
                                                ? SolarIconsOutline.unread
                                                : SolarIconsOutline.pen,
                                            color: _hasSignature
                                                ? colors.success
                                                : colors.primary,
                                            size: 24,
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        localization.drawSignature,
                                        style: textStyles.body2.copyWith(
                                          color: _hasSignature
                                              ? colors.success
                                              : colors.primary,
                                          fontWeight: _hasSignature
                                              ? FontWeight.w600
                                              : FontWeight.w400,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Container(
                                margin:
                                    const EdgeInsetsDirectional.only(start: 8)),

                            // Add Note Container
                            Expanded(
                              child: GestureDetector(
                                onTap: _showNotePopup,
                                child: Container(
                                  height: 72,
                                  padding: const EdgeInsetsDirectional.all(8),
                                  decoration: BoxDecoration(
                                    color: colors.backgroundContainer,
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                        width: 1, color: colors.primaryVariant),
                                  ),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Icon(
                                        _hasNote
                                            ? SolarIconsOutline.clipboardCheck
                                            : SolarIconsOutline
                                                .documentMedicine,
                                        color: _hasNote
                                            ? colors.success
                                            : colors.primary,
                                        size: 24,
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        localization.addNote,
                                        style: textStyles.body2.copyWith(
                                          color: _hasNote
                                              ? colors.success
                                              : colors.primary,
                                          fontSize: 12,
                                        ),
                                        textAlign: TextAlign.center,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 10),

                        // Overtime Hours
                        Container(
                          width: MediaQuery.of(context).size.width - 32,
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              12, 12, 12, 12),
                          decoration: BoxDecoration(
                            color: colors.primaryVariant,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              width: 1,
                              color: colors.primaryVariant,
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    SolarIconsOutline.clockCircle,
                                    color: colors.primary,
                                    size: 20,
                                  ),
                                  Container(
                                    margin: const EdgeInsetsDirectional.only(
                                        start: 8),
                                  ),
                                  Text(
                                    'Overtime Hours', // TODO: change to localization
                                    style: textStyles.body2.copyWith(
                                      color: colors.primary,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              Container(
                                margin: const EdgeInsetsDirectional.only(
                                    top: 8, start: 2),
                                child: Text(
                                  localization.overtimeHours(12),
                                  style: textStyles.headline2.copyWith(
                                    color: colors.primaryText,
                                    fontWeight: FontWeight.w400,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 10),

                        // Clock In
                        TimeEntryCard(
                          title: localization.clockIn,
                          time: _clockInTime,
                          location: '10 Blackstone Street, London, UK',
                          onTimeChanged: (newTime) {
                            setState(() {
                              _clockInTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),

                        // Clock Out
                        TimeEntryCard(
                          title: localization.clockOut,
                          time: _clockOutTime,
                          location: '10 Blackstone Street, London, UK',
                          onTimeChanged: (newTime) {
                            setState(() {
                              _clockOutTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),

                        // Break Time
                        TimeEntryCard(
                          title: localization.breakTime,
                          time: _breakTime,
                          location: '10 Blackstone Street, London, UK',
                          onTimeChanged: (newTime) {
                            setState(() {
                              _breakTime = newTime;
                            });
                          },
                        ),

                        Container(
                          margin: const EdgeInsets.only(top: 2),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Footer section with send shift change request button
              Container(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 24),
                decoration: BoxDecoration(
                  color: colors.backgroundContainer,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Container(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: () {
                      // Show top snackbar
                      _showTopSnackbar(context);

                      // Checking if widget is mounted before navigation
                      if (mounted && Navigator.canPop(context)) {
                        // Close the current popup
                        Navigator.of(context).pop();
                      }

                      // Reset to clock in screen if callback is provided
                      if (widget.resetToClockIn != null) {
                        widget.resetToClockIn!();
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colors.primary,
                      foregroundColor: colors.primaryText,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                    ),
                    child: Text(
                      localization.sendShiftChangeRequest,
                      style: textStyles.headline4.copyWith(
                        color: theme.colorScheme.onPrimary,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:solar_icons/solar_icons.dart';

class AddBreakModal extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;

  const AddBreakModal({
    super.key,
    this.onCancel,
    this.onSave,
  });

  @override
  State<AddBreakModal> createState() => _AddBreakModalState();
}

class _AddBreakModalState extends State<AddBreakModal> {
  final TextEditingController fromTimeController =
      TextEditingController(text: '8:00 AM');
  final TextEditingController toTimeController =
      TextEditingController(text: '8:00 AM');

  late List<String> leaveTypes;
  String selectedLeaveType = '';
  bool isDropdownOpen = false;
  bool isAllDay = false;

  @override
  void dispose() {
    fromTimeController.dispose();
    toTimeController.dispose();
    super.dispose();
  }

  // Styled time picker method based on time_entry_card.dart
  Future<void> _showStyledTimePicker(BuildContext context,
      TextEditingController controller, String helpText) async {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    final TimeOfDay? selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      helpText: helpText,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors.background,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primary
                      : colors.primaryVariant),
              dayPeriodTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              hourMinuteColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryVariant
                      : colors.backgroundContainer),
              hourMinuteTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              // Increased font size for hour and minute text
              hourMinuteTextStyle: const TextStyle(fontSize: 54),
              dialBackgroundColor: colors.backgroundContainer,
              dialHandColor: colors.primary,
              dialTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              entryModeIconColor: colors.primary,
            ),
            colorScheme: ColorScheme(
              brightness: Brightness.light,
              primary: colors.primary,
              onPrimary: colors.primaryText,
              secondary: colors.secondary,
              onSecondary: colors.secondaryText,
              error: colors.error,
              onError: colors.error,
              background: colors.background,
              onBackground: colors.primaryText,
              surface: colors.primaryVariant,
              onSurface: colors.primaryText,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedTime != null) {
      setState(() {
        // Format the time as "8:00 AM"
        final hour =
            selectedTime.hourOfPeriod == 0 ? 12 : selectedTime.hourOfPeriod;
        final period = selectedTime.period == DayPeriod.am ? 'AM' : 'PM';
        controller.text =
            '$hour:${selectedTime.minute.toString().padLeft(2, '0')} $period';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Initialize leave types with localized strings
    leaveTypes = [
      localization.leaveType,
      localization.leaveType,
      localization.leaveType,
    ];
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // height: 333,
      padding: const EdgeInsetsDirectional.fromSTEB(12, 24, 12, 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 10),
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                localization.addBreak,
                style: textStyles.body.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),

          //  from and to date
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
            child: Column(
              children: [
                // From time field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: fromTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.from,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.strokeColor),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Make it read-only as we'll use a time picker
                    onTap: () async {
                      // Show styled time picker when tapped
                      await _showStyledTimePicker(
                          context, fromTimeController, 'Break Start Time');
                    },
                  ),
                ),

                const SizedBox(height: 8),

                // To time field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: toTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.to,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.primary),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly: true,
                    onTap: () async {
                      // Show styled time picker when tapped
                      await _showStyledTimePicker(
                          context, toTimeController, 'Break End Time');
                    },
                  ),
                ),
              ],
            ),
          ),

          // Buttons section with Cancel and Save buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 16),
            child: Row(
              children: [
                // Cancel button with flex 2
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 40,
                    margin: const EdgeInsetsDirectional.only(end: 8),
                    child: OutlinedButton(
                      onPressed:
                          widget.onCancel ?? () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: colors.tertiaryText),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.cancel,
                        style: textStyles.buttonMedium.copyWith(
                          color: colors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),

                // Save button with flex 4
                Expanded(
                  flex: 4,
                  child: Container(
                    height: 40,
                    child: ElevatedButton(
                      onPressed: widget.onSave ?? () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        foregroundColor: colors.primaryText,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.save,
                        style: textStyles.buttonMedium.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

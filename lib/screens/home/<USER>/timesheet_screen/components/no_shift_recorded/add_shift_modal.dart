import 'dart:typed_data';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/timesheet_shift_details.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:signature/signature.dart';

class AddShiftModal extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;
  final VoidCallback? onNavigateToTimesheet;

  const AddShiftModal({
    super.key,
    this.onCancel,
    this.onSave,
    this.onNavigateToTimesheet,
  });

  @override
  State<AddShiftModal> createState() => _AddShiftModalState();
}

class _AddShiftModalState extends State<AddShiftModal> {
  final TextEditingController clockInTimeController =
      TextEditingController(text: '8:00 AM');
  final TextEditingController clockOutTimeController =
      TextEditingController(text: '8:00 AM');
  final TextEditingController noteController = TextEditingController();
  late SignatureController signatureController;

  late List<String> leaveTypes;
  String selectedLeaveType = '';
  bool isDropdownOpen = false;
  bool isAllDay = false;

  @override
  void initState() {
    super.initState();
    signatureController = SignatureController(
      penStrokeWidth: 3,
      penColor: Colors.black,
      exportBackgroundColor: Colors.white,
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    // Update signature controller with theme colors
    final oldPoints = signatureController.points;
    signatureController.dispose();
    signatureController = SignatureController(
      penStrokeWidth: 3,
      penColor: colors.primaryText,
      exportBackgroundColor: colors.surface,
      points: oldPoints, // Preserve any existing signature points
    );
  }

  // Styled time picker method based on time_entry_card.dart
  Future<void> _showStyledTimePicker(BuildContext context,
      TextEditingController controller, String helpText) async {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    final TimeOfDay? selectedTime = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
      helpText: helpText,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            timePickerTheme: TimePickerThemeData(
              backgroundColor: colors.background,
              hourMinuteShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodShape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              dayPeriodColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primary
                      : colors.primaryVariant),
              dayPeriodTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              hourMinuteColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryVariant
                      : colors.backgroundContainer),
              hourMinuteTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              // Increased font size for hour and minute text
              hourMinuteTextStyle: const TextStyle(fontSize: 54),
              dialBackgroundColor: colors.backgroundContainer,
              dialHandColor: colors.primary,
              dialTextColor: WidgetStateColor.resolveWith((states) =>
                  states.contains(WidgetState.selected)
                      ? colors.primaryText
                      : colors.secondaryText),
              entryModeIconColor: colors.primary,
            ),
            colorScheme: ColorScheme(
              brightness: Brightness.light,
              primary: colors.primary,
              onPrimary: colors.primaryText,
              secondary: colors.secondary,
              onSecondary: colors.secondaryText,
              error: colors.error,
              onError: colors.error,
              background: colors.background,
              onBackground: colors.primaryText,
              surface: colors.primaryVariant,
              onSurface: colors.primaryText,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: colors.primary,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedTime != null) {
      setState(() {
        // Format the time as "8:00 AM"
        final hour =
            selectedTime.hourOfPeriod == 0 ? 12 : selectedTime.hourOfPeriod;
        final period = selectedTime.period == DayPeriod.am ? 'AM' : 'PM';
        controller.text =
            '$hour:${selectedTime.minute.toString().padLeft(2, '0')} $period';
      });
    }
  }

  // Helper method to handle save with validation and navigation
  Future<void> _handleSave() async {
    // You can add validation here if needed
    // For example, check if signature is drawn:
    // if (signatureController.isEmpty) {
    //   final localization = AppLocalizations.of(context)!;
    //   ScaffoldMessenger.of(context).showSnackBar(
    //     SnackBar(content: Text(localization.pleaseDrawSignature)),
    //   );
    //   return;
    // }

    // Get signature data if needed
    // final signatureData = await signatureController.toPngBytes();

    // Get note text
    // final noteText = noteController.text.trim();

    // Close the current modal first
    Navigator.pop(context);

    // Navigate to timesheet shift details screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TimesheetShiftDetails(
          onBack: () {
            // Navigate back to timesheet popup
            if (widget.onNavigateToTimesheet != null) {
              Navigator.pop(context); // Close TimesheetShiftDetails
              widget.onNavigateToTimesheet!(); // Navigate to timesheet popup
            } else {
              // Checking if widget is mounted before navigation
              if (mounted && Navigator.canPop(context)) {
                Navigator.pop(context);
              }
            }
          },
          resetToClockIn: () {
            // When "Send Shift Change Request" is pressed, navigate back to timesheet popup
            if (widget.onNavigateToTimesheet != null) {
              // Checking if widget is mounted before navigation
              if (mounted && Navigator.canPop(context)) {
                Navigator.pop(context); // Close TimesheetShiftDetails
              }
              widget.onNavigateToTimesheet!(); // Navigate to timesheet popup
            }
          },
        ),
      ),
    );

    // Optionally call the parent's onSave callback after navigation
    if (widget.onSave != null) {
      widget.onSave!();
    }
  }

  @override
  void dispose() {
    clockInTimeController.dispose();
    clockOutTimeController.dispose();
    noteController.dispose();
    signatureController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Initialize leave types with localized strings
    leaveTypes = [
      localization.leaveType,
      localization.leaveType,
      localization.leaveType,
    ];
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // height: 333,
      padding: const EdgeInsetsDirectional.fromSTEB(12, 24, 12, 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 10),
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                localization.addShift,
                style: textStyles.body.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),

          //  clock in and clock out time
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
            child: Column(
              children: [
                // Clock in time field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: clockInTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.from,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.strokeColor),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Make it read-only as we'll use a time picker
                    onTap: () async {
                      // Show styled time picker when tapped
                      await _showStyledTimePicker(
                          context, clockInTimeController, 'Clock In Time');
                    },
                  ),
                ),

                const SizedBox(height: 8),

                // To time field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.strokeColor,
                      width: 1,
                    ),
                  ),
                  child: TextField(
                    controller: clockOutTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.to,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.primary),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly: true,
                    onTap: () async {
                      // Show styled time picker when tapped
                      await _showStyledTimePicker(
                          context, clockOutTimeController, 'Clock Out Time');
                    },
                  ),
                ),
              ],
            ),
          ),

          // Note input section
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 4, 10, 8),
            child: Container(
              height: 100,
              decoration: BoxDecoration(
                color: colors.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.strokeColor,
                  width: 1,
                ),
              ),
              child: TextField(
                controller: noteController,
                maxLines: null,
                expands: true,
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                ),
                decoration: InputDecoration(
                  hintText: localization.enterYourNoteHere,
                  hintStyle: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsetsDirectional.all(16),
                ),
              ),
            ),
          ),

          // Signature section
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 12, 10, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Draw signature label
                Container(
                  margin: const EdgeInsetsDirectional.only(bottom: 12),
                  child: Text(
                    localization.drawSignature,
                    style: textStyles.body.copyWith(
                      color: colors.primary,
                    ),
                  ),
                ),
                // Signature pad container
                Container(
                  height: 168,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: colors.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colors.primaryVariant,
                      width: 1,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Signature(
                      controller: signatureController,
                      backgroundColor: colors.surface,
                      width: double.infinity,
                      height: double.infinity,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Buttons section with Cancel and Save buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 16),
            child: Row(
              children: [
                // Cancel button with flex 2
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 40,
                    margin: const EdgeInsetsDirectional.only(end: 8),
                    child: OutlinedButton(
                      onPressed:
                          widget.onCancel ?? () => Navigator.pop(context),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: colors.tertiaryText),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.cancel,
                        style: textStyles.buttonMedium.copyWith(
                          color: colors.secondaryText,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),

                // Save button with flex 4
                Expanded(
                  flex: 4,
                  child: Container(
                    height: 40,
                    child: ElevatedButton(
                      onPressed: _handleSave,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        foregroundColor: colors.primaryText,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        localization.save,
                        style: textStyles.buttonMedium.copyWith(
                          color: theme.colorScheme.onPrimary,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

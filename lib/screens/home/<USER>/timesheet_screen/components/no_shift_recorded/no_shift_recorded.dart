import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/no_shift_recorded/add_leave_modal.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/no_shift_recorded/add_break_modal.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/components/no_shift_recorded/add_shift_modal.dart';
import 'package:ako_basma/screens/home/<USER>/timesheet_screen/timesheet_popup.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class NoShiftRecorded extends StatelessWidget {
  final DateTime selectedDate;

  const NoShiftRecorded({
    super.key,
    required this.selectedDate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context);

    // Format date helper function
    String formatDateWithMonth(DateTime date) {
      const months = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ];
      return '${date.day} ${months[date.month - 1]}';
    }

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        top: true,
        bottom: false,
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      // Safe navigation check before popping
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      }
                    },
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(start: 16),
                  ),
                  Text(
                    localization.shiftDetails,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Main content - centered
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Calendar grid icon
                  Container(
                    margin: const EdgeInsetsDirectional.only(bottom: 24),
                    child: Column(
                      children: [
                        // Calendar image
                        Image.asset(
                          theme.brightness == Brightness.light
                              ? 'assets/images/shift_calendar_light.png'
                              : 'assets/images/shift_calendar_dark.png',
                          width: 90,
                          height: 80,
                          fit: BoxFit.cover,
                        ),
                      ],
                    ),
                  ),

                  // No shift recorded text
                  Text(
                    'No Shift Recorded On ${formatDateWithMonth(selectedDate)}',
                    style: textStyles.headline3.copyWith(
                      color: colors.primaryText,
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // Bottom action buttons
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 32),
              color: colors.backgroundContainer,
              child: Column(
                children: [
                  // Add Leave and Add Break buttons row
                  Row(
                    children: [
                      // Add Leave button
                      Expanded(
                        child: Container(
                          height: 48,
                          margin: const EdgeInsetsDirectional.only(end: 4),
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                color: colors.info,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            onPressed: () => _showAddLeaveModal(context),
                            child: Text(
                              localization.addLeave,
                              style: textStyles.body.copyWith(
                                color: colors.info,
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Add Break button
                      Expanded(
                        child: Container(
                          height: 48,
                          margin: const EdgeInsetsDirectional.only(start: 4),
                          child: OutlinedButton(
                            style: OutlinedButton.styleFrom(
                              side: BorderSide(
                                color: colors.primary,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            onPressed: () => _showAddBreakModal(context),
                            child: Text(
                              localization.addBreak,
                              style: textStyles.body.copyWith(
                                color: colors.primary,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  // Add Shift button
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 8),
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () => _showAddShiftModal(context),
                      child: Text(
                        localization.addShift,
                        style: textStyles.headline4.copyWith(
                          color: theme.colorScheme.onPrimary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Show Add Leave modal
  void _showAddLeaveModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddLeaveModal(),
    );
  }

  // Show Add Break modal
  void _showAddBreakModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddBreakModal(),
    );
  }

  // Show Add Shift modal
  void _showAddShiftModal(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddShiftModal(
        onNavigateToTimesheet: () {
          // Close current NoShiftRecorded screen and navigate to TimesheetPopup
          if (Navigator.canPop(context)) {
            Navigator.pop(context); // Close NoShiftRecorded
          }
          Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (context) => const TimesheetPopup(),
            ),
          );
        },
      ),
    );
  }
}

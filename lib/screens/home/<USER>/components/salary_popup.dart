import 'package:ako_basma/screens/home/<USER>/components/salary_detail_popup.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';
import 'package:solar_icons/solar_icons.dart';

class SalaryPopup extends StatefulWidget {
  final VoidCallback? onBack;

  const SalaryPopup({
    super.key,
    this.onBack,
  });

  @override
  State<SalaryPopup> createState() => _SalaryPopupState();
}

class _SalaryPopupState extends State<SalaryPopup> {
  late String selectedMonth;
  late String selectedYear;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Initialize dropdown values with localized strings
    selectedMonth = localization.month;
    selectedYear = localization.year;

    // Sample data for salary entries with mixed status (paid and pending)
    final List<Map<String, dynamic>> salaryEntries = [
      {
        'date': '01/12/2024',
        'amount': 'IQD 100,000,000',
        'status': 'Paid',
        'unpaid': false
      },
      {
        'date': '15/11/2024',
        'amount': 'IQD 100,000,000',
        'status': 'Paid',
      },
      {
        'date': '01/11/2024',
        'amount': 'IQD 100,000,000',
        'status': 'Unpaid',
        'unpaid': true
      },
      {
        'date': '01/10/2024',
        'amount': 'IQD 100,000,000',
        'status': 'Paid',
        'unpaid': false
      },
      {
        'date': '01/09/2024',
        'amount': 'IQD 100,000,000',
        'status': 'Paid',
        'unpaid': false
      },
      {
        'date': '01/08/2024',
        'amount': 'IQD 100,000,000',
        'status': 'Paid',
        'unpaid': false
      },
    ];

    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  Container(
                      margin: const EdgeInsetsDirectional.only(start: 12)),
                  Text(
                    localization.salary,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            Container(
              padding: const EdgeInsetsDirectional.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  // Month dropdown
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      padding:
                          const EdgeInsetsDirectional.symmetric(horizontal: 16),
                      height: 48,
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: selectedMonth,
                          icon: Icon(
                            SolarIconsOutline.altArrowDown,
                            color: colors.primary,
                            size: 22,
                          ),
                          isExpanded: true,
                          dropdownColor: colors.backgroundContainer,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedMonth = newValue!;
                            });
                          },
                          items: <String>[
                            localization.month,
                            'January',
                            'February',
                            'March',
                            'April',
                            'May',
                            'June',
                            'July',
                            'August',
                            'September',
                            'October',
                            'November',
                            'December'
                          ].map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),

                  // Year dropdown
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(start: 8),
                      padding:
                          const EdgeInsetsDirectional.symmetric(horizontal: 16),
                      height: 48,
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: selectedYear,
                          icon: Icon(
                            SolarIconsOutline.altArrowDown,
                            color: colors.primary,
                            size: 22,
                          ),
                          isExpanded: true,
                          dropdownColor: colors.backgroundContainer,
                          style: textStyles.body2.copyWith(
                            color: colors.secondaryText,
                          ),
                          onChanged: (String? newValue) {
                            setState(() {
                              selectedYear = newValue!;
                            });
                          },
                          items: <String>[
                            localization.year,
                            '2024',
                            '2023',
                            '2022',
                            '2021'
                          ].map<DropdownMenuItem<String>>((String value) {
                            return DropdownMenuItem<String>(
                              value: value,
                              child: Text(value),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // Salary entries list
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                itemCount: salaryEntries.length,
                itemBuilder: (context, index) {
                  final entry = salaryEntries[index];
                  return GestureDetector(
                    onTap: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder: (context) => SalaryDetailPopup(
                          salaryData: entry,
                        ),
                      );
                    },
                    child: Container(
                      width: 328,
                      margin: const EdgeInsetsDirectional.only(bottom: 8),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      padding:
                          const EdgeInsetsDirectional.fromSTEB(12, 12, 12, 12),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (index == 0)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  localization.thisMonthsSalary,
                                  style: textStyles.body.copyWith(
                                    color: colors.primary,
                                  ),
                                ),
                                Container(
                                  margin:
                                      const EdgeInsetsDirectional.only(top: 8),
                                ),
                              ],
                            ),

                          // Date and status row
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                entry['date'],
                                style: textStyles.body.copyWith(
                                  color: colors.tertiaryText,
                                ),
                              ),
                              Container(
                                padding: const EdgeInsetsDirectional.fromSTEB(
                                    16, 8, 16, 8),
                                decoration: BoxDecoration(
                                  // Use warning colors for pending status, success for paid
                                  color: entry['unpaid'] == true
                                      ? colors.warningContainer
                                      : colors.successContainer,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  getLabelTranslation(entry['status'], context),
                                  style: textStyles.body2.copyWith(
                                    // Use warning text color for pending, success for paid
                                    color: entry['unpaid'] == true
                                        ? colors.warning
                                        : colors.success,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          Container(
                            margin: const EdgeInsetsDirectional.only(top: 8),
                          ),

                          // Salary details card
                          Container(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16, 12, 16, 12),
                            decoration: BoxDecoration(
                              color: colors.backgroundContainer,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: colors.strokeColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  localization.netSalary,
                                  style: textStyles.body.copyWith(
                                    color: colors.tertiaryText,
                                  ),
                                ),
                                Text(
                                  entry['amount'],
                                  style: textStyles.body.copyWith(
                                    color: colors.secondaryText,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

String getLabelTranslation(String label, BuildContext context) {
  final localization = AppLocalizations.of(context);
  switch (label.toLowerCase()) {
    case 'paid':
      return localization.paid;
    case 'unpaid':
      return localization.unpaid;
    default:
      return label; // Fallback to original label if unknown.
  }
}

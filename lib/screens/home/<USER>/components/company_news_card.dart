import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';

class CompanyNewsCard extends StatelessWidget {
  const CompanyNewsCard({
    super.key,
    required this.imagePath,
    this.margin,
  });

  /// Path of the image to show inside the card.
  final String imagePath;

  /// Optional margin around the card.
  /// items in a carousel so the spacing can be controlled
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;

    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Background image
            Image.asset(
              imagePath,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => Container(
                color: colors.backgroundContainer,
                alignment: Alignment.center,
                child: Icon(
                  Icons.image,
                  color: colors.tertiaryText,
                  size: 24,
                ),
              ),
            ),
            // Gradient overlay
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Color(0xBF000000), // 75% opacity black
                    Color(0x00000000), // 0% opacity black (transparent)
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

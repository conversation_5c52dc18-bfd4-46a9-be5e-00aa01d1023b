import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:solar_icons/solar_icons.dart';

import 'package:intl/intl.dart';
import 'package:ako_basma/components/time_picker/rounded_time_picker.dart';

class RequestLeavePopup extends StatefulWidget {
  final VoidCallback? onCancel;
  final VoidCallback? onSave;

  const RequestLeavePopup({
    super.key,
    this.onCancel,
    this.onSave,
  });

  @override
  State<RequestLeavePopup> createState() => _RequestLeavePopupState();
}

class _RequestLeavePopupState extends State<RequestLeavePopup> {
  final TextEditingController fromTimeController =
      TextEditingController(text: '8:00 AM');
  final TextEditingController toTimeController =
      TextEditingController(text: '8:00 AM');

  late List<String> leaveTypes;
  String selectedLeaveType = '';
  bool isDropdownOpen = false;
  bool isAllDay = false;

  @override
  void dispose() {
    fromTimeController.dispose();
    toTimeController.dispose();
    super.dispose();
  }

  /// toggle switch changes for all day leave when on
  void _handleAllDayToggle(bool value) {
    setState(() {
      isAllDay = value;

      if (value) {
        // Format today's date for all day leave
        final now = DateTime.now();
        final dateFormatter = DateFormat('dd/MM/yyyy');
        final todayDate = dateFormatter.format(now);

        fromTimeController.text = todayDate;
        toTimeController.text = todayDate;
      } else {
        // Revert to default time format
        fromTimeController.text = '8:00 AM';
        toTimeController.text = '8:00 AM';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    // Initialize leave types with localized strings
    leaveTypes = [
      localization.leaveType,
      localization.leaveType,
      localization.leaveType,
    ];
    return Container(
      decoration: BoxDecoration(
        color: colors.backgroundContainer,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      // height: 333,
      padding: const EdgeInsetsDirectional.fromSTEB(12, 24, 12, 24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 0, 10, 10),
            child: Align(
              alignment: AlignmentDirectional.centerStart,
              child: Text(
                localization.requestForLeave,
                style: textStyles.body.copyWith(
                  color: colors.primary,
                ),
              ),
            ),
          ),

          // dropdown button
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 6),
            child: Column(
              children: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      // isDropdownOpen = !isDropdownOpen;
                    });
                  },
                  child: Container(
                    padding:
                        const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          selectedLeaveType.isEmpty
                              ? localization.leaveType
                              : selectedLeaveType,
                          style: textStyles.body2.copyWith(
                            color: selectedLeaveType.isEmpty
                                ? colors.tertiaryText
                                : colors.primaryText,
                          ),
                        ),
                        Icon(
                          SolarIconsOutline.altArrowDown,
                          color: colors.primary,
                        ),
                      ],
                    ),
                  ),
                ),
                // below code is not given in the figma designs but maybe for future reference I've added it

                if (isDropdownOpen)
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 4),
                    constraints: BoxConstraints(maxHeight: 150),
                    decoration: BoxDecoration(
                      color: colors.backgroundContainer,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: colors.strokeColor,
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ListView.builder(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: leaveTypes.length,
                      itemBuilder: (context, index) {
                        return InkWell(
                          onTap: () {
                            setState(() {
                              selectedLeaveType = leaveTypes[index];
                              isDropdownOpen = false;
                            });
                          },
                          child: Container(
                            padding: const EdgeInsetsDirectional.fromSTEB(
                                16, 12, 16, 12),
                            child: Text(
                              leaveTypes[index],
                              style: textStyles.body2.copyWith(
                                color: colors.primaryText,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
              ],
            ),
          ),

          // toggle switch
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 6, 10, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  localization.allDay,
                  style: textStyles.body.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
                Transform.scale(
                  scale: 0.8,
                  child: CupertinoSwitch(
                    value: isAllDay,
                    onChanged: _handleAllDayToggle,
                    activeColor: colors.primary,
                    thumbColor: theme.colorScheme.onPrimary,
                    trackColor: colors.strokeColor,
                  ),
                ),
              ],
            ),
          ),

          //  from and to date/time fields
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 8),
            child: Column(
              children: [
                // From time/date field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: fromTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.from,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.strokeColor),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Always read-only as we handle with pickers
                    onTap: () async {
                      if (isAllDay) {
                        // Show date picker for all day leave
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate:
                              DateTime.now().add(const Duration(days: 365)),
                        );
                        if (picked != null) {
                          setState(() {
                            final dateFormatter = DateFormat('dd/MM/yyyy');
                            fromTimeController.text =
                                dateFormatter.format(picked);
                          });
                        }
                      } else {
                        // Show time picker for partial day leave
                        final TimeOfDay? picked = await showRoundedTimePicker(
                          context,
                        );
                        if (picked != null) {
                          setState(() {
                            // Format the time as "8:00 AM"
                            final hour = picked.hourOfPeriod == 0
                                ? 12
                                : picked.hourOfPeriod;
                            final period =
                                picked.period == DayPeriod.am ? 'AM' : 'PM';
                            fromTimeController.text =
                                '$hour:${picked.minute.toString().padLeft(2, '0')} $period';
                          });
                        }
                      }
                    },
                  ),
                ),

                const SizedBox(height: 8),

                // To time/date field
                Container(
                  decoration: BoxDecoration(
                    color: colors.backgroundContainer,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: TextField(
                    controller: toTimeController,
                    decoration: InputDecoration(
                      contentPadding:
                          const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
                      border: InputBorder.none,
                      labelText: localization.to,
                      floatingLabelBehavior: FloatingLabelBehavior.auto,
                      labelStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: colors.strokeColor,
                        ),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colors.primary),
                      ),
                    ),
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    readOnly:
                        true, // Always read-only as we handle with pickers
                    onTap: () async {
                      if (isAllDay) {
                        // Show date picker for all day leave
                        final DateTime? picked = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate:
                              DateTime.now().add(const Duration(days: 365)),
                        );
                        if (picked != null) {
                          setState(() {
                            final dateFormatter = DateFormat('dd/MM/yyyy');
                            toTimeController.text =
                                dateFormatter.format(picked);
                          });
                        }
                      } else {
                        // Show time picker for partial day leave
                        final TimeOfDay? picked = await showRoundedTimePicker(
                          context,
                        );
                        if (picked != null) {
                          setState(() {
                            // Format the time as "8:00 AM"
                            final hour = picked.hourOfPeriod == 0
                                ? 12
                                : picked.hourOfPeriod;
                            final period =
                                picked.period == DayPeriod.am ? 'AM' : 'PM';
                            toTimeController.text =
                                '$hour:${picked.minute.toString().padLeft(2, '0')} $period';
                          });
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),

          // Buttons
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(10, 8, 10, 16),
            child: Container(
              height: 40,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: widget.onSave ?? () => Navigator.pop(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colors.primary,
                  foregroundColor: colors.primaryText,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  localization.submit,
                  style: textStyles.buttonMedium.copyWith(
                    color: theme.colorScheme.onPrimary,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

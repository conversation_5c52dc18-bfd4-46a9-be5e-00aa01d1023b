import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/util/location/location.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class Location extends StatefulWidget {
  const Location({super.key});

  @override
  State<Location> createState() => _LocationState();
}

class _LocationState extends State<Location>
    with AutomaticKeepAliveClientMixin {
  GoogleMapController? _mapController;
  LatLng? _currentLocation;
  bool _isLoadingLocation = true;

  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();
    // Start with a default location and then get current location
    _currentLocation =
        const LatLng(37.7749, -122.4194); // San Francisco default
    _getCurrentLocation();
  }

  String? _getMapStyle(BuildContext context) {
    // Set map style based on theme
    if (Theme.of(context).brightness == Brightness.dark) {
      return '''
        [
          {
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#212121"
              }
            ]
          },
          {
            "elementType": "labels.icon",
            "stylers": [
              {
                "visibility": "off"
              }
            ]
          },
          {
            "elementType": "labels.text.fill",
            "stylers": [
              {
                "color": "#757575"
              }
            ]
          },
          {
            "elementType": "labels.text.stroke",
            "stylers": [
              {
                "color": "#212121"
              }
            ]
          },
          {
            "featureType": "administrative",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#757575"
              }
            ]
          },
          {
            "featureType": "road",
            "elementType": "geometry.fill",
            "stylers": [
              {
                "color": "#2c2c2c"
              }
            ]
          },
          {
            "featureType": "water",
            "elementType": "geometry",
            "stylers": [
              {
                "color": "#000000"
              }
            ]
          }
        ]
      ''';
    } else {
      return null; // Use default light style
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      final location = await getCurrentLocation();
      if (location != null && mounted) {
        setState(() {
          _currentLocation = location;
          _isLoadingLocation = false;
        });

        // Move camera to current location if map controller is available
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: location,
              zoom: 16.0,
            ),
          ),
        );
      } else {
        // No location available, use default and stop loading
        if (mounted) {
          setState(() {
            _isLoadingLocation = false;
            // Add a default marker at San Francisco
            _markers = {
              Marker(
                markerId: const MarkerId('default_location'),
                position: _currentLocation!,
                icon: BitmapDescriptor.defaultMarkerWithHue(
                    BitmapDescriptor.hueRed),
                infoWindow: const InfoWindow(
                  title: 'Default Location',
                  snippet: 'San Francisco, CA',
                ),
              ),
            };
          });
        }
      }
    } catch (e) {
      // Error getting location - use default location
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
          // Add a default marker at San Francisco
          _markers = {
            Marker(
              markerId: const MarkerId('default_location'),
              position: _currentLocation!,
              icon: BitmapDescriptor.defaultMarkerWithHue(
                  BitmapDescriptor.hueRed),
              infoWindow: const InfoWindow(
                title: 'Default Location',
                snippet: 'San Francisco, CA',
              ),
            ),
          };
        });
      }
    }
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    // If we already have location, move camera
    if (_currentLocation != null) {
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: _currentLocation!,
            zoom: 16.0,
          ),
        ),
      );
    }
  }

  @override
  bool get wantKeepAlive =>
      true; // by this the map will be alive even when navigating between screens and we used it with AutomaticKeepAliveClientMixin

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final screenWidth = MediaQuery.of(context).size.width;
    final containerWidth = screenWidth - 32; // Full width minus margins

    return Container(
      margin: const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 4),
      width: containerWidth,
      height: 220,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: colors.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: Stack(
          children: [
            // Google Map
            GoogleMap(
              onMapCreated: _onMapCreated,
              initialCameraPosition: CameraPosition(
                target: _currentLocation!,
                zoom: 16.0,
              ),
              markers: _markers,
              style: _getMapStyle(context),
              myLocationEnabled: false, // We'll use custom marker
              myLocationButtonEnabled: false,
              zoomControlsEnabled: false,
              mapToolbarEnabled: false,
              compassEnabled: false,
              rotateGesturesEnabled: false,
              scrollGesturesEnabled: true,
              zoomGesturesEnabled: true,
              tiltGesturesEnabled: false,
              mapType: MapType.normal,
              liteModeEnabled: false, // Ensure full map mode
              buildingsEnabled: true,
              trafficEnabled: false,
              onTap: (LatLng position) {
                // Handle map tap if needed
              },
            ),

            // Loading indicator
            if (_isLoadingLocation)
              Container(
                color: colors.surface.withOpacity(0.8),
                child: Center(
                  child: CircularProgressIndicator(
                    color: colors.primary,
                  ),
                ),
              ),

            // Custom user location marker overlay (centered) - only show if map is loaded
            if (!_isLoadingLocation && _currentLocation != null)
              Center(
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: colors.primary.withOpacity(0.2),
                  ),
                  child: Container(
                    padding: const EdgeInsetsDirectional.all(2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primary.withOpacity(0.7),
                    ),
                    child: CircleAvatar(
                      radius: 12,
                      backgroundColor: colors.primary,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: const Icon(
                          SolarIconsBold.user,
                          size: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }
}

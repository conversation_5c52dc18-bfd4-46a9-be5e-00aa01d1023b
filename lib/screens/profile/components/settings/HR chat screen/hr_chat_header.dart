import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/labels.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

/// HR Chat header component
class HrChatHeader extends StatelessWidget {
  final VoidCallback onBackPressed;

  const HrChatHeader({
    super.key,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsetsDirectional.only(
        start: 16,
        end: 16,
        top: MediaQuery.of(context).padding.top + 8, // Blend with status bar
        bottom: 12,
      ),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onBackPressed,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                child: Icon(
                  DirectionHelpers.getBackArrowIcon(context),
                  size: 32,
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // HR Profile section
          Expanded(
            child: Container(
              margin: const EdgeInsetsDirectional.only(start: 8),
              child: Row(
                children: [
                  // HR Profile picture
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primaryVariant,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/person.png',
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback to icon if image fails to load
                          return Container(
                            color: colors.primary.withOpacity(0.1),
                            child: Icon(
                              Icons.person,
                              size: 24,
                              color: colors.primary,
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // HR Name and Last Seen
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(start: 6),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // HR Name
                          Text(
                            'HR',
                            style: textStyles.headline3.copyWith(
                              color: colors.secondaryText,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),

                          // Last Seen
                          Container(
                            margin: const EdgeInsetsDirectional.only(top: 8),
                            child: Text(
                              'Last seen on 12:00 PM',
                              style: textStyles.body3.copyWith(
                                color: colors.tertiaryText,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class Logout extends StatefulWidget {
  const Logout({super.key});

  @override
  State<StatefulWidget> createState() {
    return _LogoutState();
  }
}

class _LogoutState extends State<Logout> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Dialog(
      backgroundColor: Colors.transparent,
      // insetPadding: const EdgeInsetsDirectional.symmetric(horizontal: 16, vertical: 24),
      child: Container(
        // width: MediaQuery.of(context).size.width * 0.9,
        padding: const EdgeInsetsDirectional.fromSTEB(24, 24, 24, 24),
        decoration: BoxDecoration(
          color: colors.backgroundContainer,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Logout Icon
            Container(
              width: 100,
              height: 70,
              decoration: BoxDecoration(
                color: colors.errorContainer,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: SvgPicture.asset(
                  'assets/icons/profile_screen/logout.svg',
                  width: 35,
                  height: 35,
                ),
              ),
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 24),
            ),
            // Title
            Text(
              localization.logout,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
              ),
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 16),
            ),
            // Confirmation Message
            Text(
              localization.logoutConfirmation,
              style: textStyles.body2.copyWith(
                color: colors.secondaryText,
              ),
              textAlign: TextAlign.center,
            ),
            Container(
              margin: const EdgeInsetsDirectional.only(top: 20),
            ),
            // Buttons Row
            Row(
              children: [
                // Cancel Button
                Expanded(
                  flex: 2,
                  child: Container(
                    height: 44,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: colors.strokeColor,
                        width: 1,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Center(
                          child: Text(
                            localization.cancel,
                            style: textStyles.body.copyWith(
                              color: colors.secondaryText,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsetsDirectional.only(start: 12),
                ),
                // Log Out Button
                Expanded(
                  flex: 4,
                  child: Container(
                    height: 44,
                    decoration: BoxDecoration(
                      color: colors.error,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(8),
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Center(
                          child: Text(
                            localization.logout,
                            style: textStyles.body.copyWith(
                              color: theme.colorScheme.onPrimary,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

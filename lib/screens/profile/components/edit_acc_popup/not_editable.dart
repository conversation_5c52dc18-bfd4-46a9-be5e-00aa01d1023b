import 'package:ako_basma/labels.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';

class NotEditable extends StatefulWidget {
  const NotEditable({super.key});

  @override
  State<NotEditable> createState() => _NotEditableState();
}

class _NotEditableState extends State<NotEditable> {
  final TextEditingController jobTitleController = TextEditingController();
  final TextEditingController departmentController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 18, 16, 8),
      child: Column(
        children: [
          // Account title
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  localization.notEditable,
                  style: textStyles.body.copyWith(
                    color: colors.primaryText,
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Text(
                  localization.adminContactMessage,
                  style: textStyles.body3.copyWith(
                    color: colors.tertiaryText,
                  ),
                ),
              ],
            ),
          ),
          // Title field
          TextField(
            controller: jobTitleController,
            decoration: InputDecoration(
              contentPadding:
                  const EdgeInsetsDirectional.fromSTEB(16, 12, 16, 12),
              labelText: localization.jobTitle,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                ),
              ),
            ),
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
          ),

          const SizedBox(
            height: 14,
          ),

          // Description field
          TextField(
            controller: departmentController,
            maxLines: 1,
            decoration: InputDecoration(
              contentPadding: const EdgeInsetsDirectional.fromSTEB(
                16,
                12,
                16,
                12,
              ),
              labelText: localization.department,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                ),
              ),
            ),
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
          ),

          Container(
            height: 8,
          ),
        ],
      ),
    );
  }
}

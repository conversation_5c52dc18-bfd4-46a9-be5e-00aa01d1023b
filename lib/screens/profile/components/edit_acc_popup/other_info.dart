import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:solar_icons/solar_icons.dart';

class OtherInfo extends StatefulWidget {
  const OtherInfo({super.key});

  @override
  State<OtherInfo> createState() => _OtherInfoState();
}

class _OtherInfoState extends State<OtherInfo> {
  // Controllers for date fields
  final TextEditingController dateOfBirthController = TextEditingController();
  final TextEditingController startDateController = TextEditingController();

  // Date values
  DateTime? selectedDateOfBirth;
  DateTime? selectedStartDate;

  // Country dropdown value
  String? selectedCountry;

  @override
  void initState() {
    super.initState();

    //sample country
    // selectedCountry = 'Iraq';
  }

  @override
  void dispose() {
    dateOfBirthController.dispose();
    startDateController.dispose();
    super.dispose();
  }

  // Function to show date picker
  Future<void> _selectDate(BuildContext context, bool isDateOfBirth) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isDateOfBirth
          ? (selectedDateOfBirth ?? DateTime.now())
          : (selectedStartDate ?? DateTime.now()),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        final theme = Theme.of(context);
        final colors = theme.extension<AppColors>()!;
        return Theme(
          data: theme.copyWith(
            colorScheme: theme.colorScheme.copyWith(
              primary: colors.primary,
              onPrimary: Colors.white,
              surface: colors.backgroundContainer,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isDateOfBirth) {
          selectedDateOfBirth = picked;
          dateOfBirthController.text = DateFormat('dd/MM/yyyy').format(picked);
        } else {
          selectedStartDate = picked;
          startDateController.text = DateFormat('dd/MM/yyyy').format(picked);
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Other Information section title
          Container(
            width: double.infinity,
            margin: const EdgeInsetsDirectional.only(bottom: 16),
            padding: const EdgeInsetsDirectional.symmetric(vertical: 4),
            child: Text(
              localization.otherInfo,
              style: textStyles.body.copyWith(
                color: colors.primaryText,
              ),
              maxLines: 2,
              overflow: TextOverflow.visible,
              softWrap: true,
            ),
          ),

          // Date of Birth field with date picker
          GestureDetector(
            onTap: () => _selectDate(context, true),
            child: AbsorbPointer(
              child: TextField(
                controller: dateOfBirthController,
                decoration: InputDecoration(
                  contentPadding: const EdgeInsetsDirectional.fromSTEB(
                    16,
                    18,
                    16,
                    18,
                  ),
                  labelText: localization.dateOfBirth,
                  floatingLabelBehavior: FloatingLabelBehavior.auto,
                  labelStyle: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                  filled: true,
                  fillColor: colors.backgroundContainer,
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: colors.strokeColor,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: colors.primary,
                    ),
                  ),
                ),
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          const SizedBox(
            height: 14,
          ),

          // Country dropdown field
          DropdownButtonFormField<String>(
            value: selectedCountry,
            decoration: InputDecoration(
              contentPadding: const EdgeInsetsDirectional.fromSTEB(
                16,
                14,
                16,
                14,
              ),
              labelText: localization.country,
              floatingLabelBehavior: FloatingLabelBehavior.auto,
              labelStyle: textStyles.body2.copyWith(
                color: colors.tertiaryText,
              ),
              filled: true,
              fillColor: colors.backgroundContainer,
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.strokeColor,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: colors.primary,
                ),
              ),
            ),
            style: textStyles.body2.copyWith(
              color: colors.primaryText,
            ),
            icon: Icon(
              SolarIconsOutline.altArrowDown,
              color: colors.primaryText,
              size: 24,
            ),
            dropdownColor: colors.backgroundContainer,
            items: [
              DropdownMenuItem(
                value: 'Iraq',
                child: Text(
                  localization.iraq,
                  style: textStyles.body2.copyWith(
                    color: colors.primaryText,
                  ),
                ),
              ),
              // DropdownMenuItem(
              //   value: 'Iraq',
              //   child: Text(
              //     localization.iraq,
              //     style: textStyles.body2.copyWith(
              //       color: colors.primaryText,
              //     ),
              //   ),
              // ),
            ],
            onChanged: (value) {
              setState(() {
                selectedCountry = value;
              });
            },
          ),

          const SizedBox(
            height: 14,
          ),

          // Start Date field with date picker
          GestureDetector(
            onTap: () => _selectDate(context, false),
            child: AbsorbPointer(
              child: TextField(
                controller: startDateController,
                decoration: InputDecoration(
                  // Increased padding for larger field height
                  contentPadding: const EdgeInsetsDirectional.fromSTEB(
                    16,
                    18,
                    16,
                    18,
                  ),
                  labelText: localization.startDate,
                  floatingLabelBehavior: FloatingLabelBehavior.auto,
                  labelStyle: textStyles.body2.copyWith(
                    color: colors.tertiaryText,
                  ),
                  filled: true,
                  fillColor: colors.backgroundContainer,

                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: colors.strokeColor,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: colors.primary,
                    ),
                  ),
                ),
                style: textStyles.body2.copyWith(
                  color: colors.primaryText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

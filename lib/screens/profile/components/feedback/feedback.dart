import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:flutter/material.dart';

class Feedback extends StatefulWidget {
  const Feedback({super.key});

  @override
  State<StatefulWidget> createState() {
    return _FeedbackState();
  }
}

class _FeedbackState extends State<Feedback> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Container(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 14, 0, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localization.feedback,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
              fontWeight: FontWeight.w600,
            ),
          ),
          Container(
            margin: const EdgeInsetsDirectional.only(top: 8),
            child: Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 12),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colors.primaryVariant,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with manager's feedback title and time
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          localization.managersFeedback,
                          style: textStyles.body3.copyWith(
                            color: colors.primaryText,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Text(
                        '15 March 2025',
                        style: textStyles.body3.copyWith(
                          color: colors.tertiaryText,
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: const EdgeInsetsDirectional.only(top: 12),
                  ),
                  // Feedback message
                  Text(
                    'Excellent work on the recent project. Keep up the great effort',
                    style: textStyles.body2.copyWith(
                      color: colors.secondaryText,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

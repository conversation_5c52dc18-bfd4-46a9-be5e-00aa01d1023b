import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class Details extends StatefulWidget {
  const Details({super.key});

  @override
  State<Details> createState() => _DetailsState();
}

class _DetailsState extends State<Details> {
  @override
  Widget build(BuildContext context) {
    final localization = AppLocalizations.of(context)!;

    return Container(
      child: Column(
        children: [
          details(context, SolarIconsOutline.letter, localization.email,
              '<EMAIL>'),
          details(context, SolarIconsOutline.phone, localization.phone,
              '+1234567890'),
          details(context, SolarIconsOutline.calendarMinimalistic,
              localization.dateOfBirth, '01/12/2004'),
          details(context, SolarIconsOutline.calendarMinimalistic,
              localization.startDate, '01/01/2020'),
        ],
      ),
    );
  }
}

Widget details(BuildContext context, IconData icon, String timeDurationText,
    String actualTime) {
  final theme = Theme.of(context);
  final colors = theme.extension<AppColors>()!;
  final textStyles = theme.extension<TextStyles>()!;
  return Container(
    margin: const EdgeInsetsDirectional.fromSTEB(13, 2, 13, 2),
    width: double.infinity,
    child: Container(
      padding: const EdgeInsetsDirectional.fromSTEB(8, 4, 8, 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colors.primaryVariant,
          width: 1,
        ),
        color: colors.background,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Icon(
            icon,
            size: 16,
            color: colors.secondaryText,
          ),
          const SizedBox(width: 6),
          Expanded(
            child: Text(
              timeDurationText,
              style: textStyles.body3.copyWith(
                color: colors.secondaryText,
                fontSize: 10,
              ),
            ),
          ),
          Text(
            actualTime,
            style: textStyles.body3.copyWith(
              color: colors.secondaryText,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    ),
  );
}

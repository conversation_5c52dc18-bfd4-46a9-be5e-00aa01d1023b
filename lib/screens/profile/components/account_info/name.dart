import 'package:ako_basma/components/image/image_widget.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:flutter/material.dart';
import 'package:solar_icons/solar_icons.dart';

class Name extends StatefulWidget {
  const Name({super.key});

  @override
  State<Name> createState() => _NameState();
}

class _NameState extends State<Name> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>();
    final textStyles = theme.extension<TextStyles>();
    final localization = AppLocalizations.of(context)!;

    return Container(
      height: 48,
      width: double.infinity,
      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Stack(
                children: [
                  Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: colors!.tertiaryText,
                        width: 1,
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: const ImageContainer(
                        url: null,
                        placeholderAsset: 'assets/images/person.png',
                        height: 40,
                        width: 40,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  PositionedDirectional(
                    end: 0,
                    bottom: 2,
                    child: Container(
                      height: 16,
                      width: 16,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: colors.primary,
                        border: Border.all(
                          color: Colors.white,
                          width: 2,
                        ),
                      ),
                      child: const Icon(
                        SolarIconsBold.pen,
                        size: 8,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
              Container(
                width: 12,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Nada Jaafar Uday',
                    style: textStyles!.body3.copyWith(
                      color: colors.secondaryText,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'UI/UX Designer',
                    style: textStyles.body3.copyWith(
                      color: colors.primaryText,
                    ),
                  ),
                ],
              ),
            ],
          ),
          Container(
            height: 26,
            width: 75,
            padding: const EdgeInsetsDirectional.fromSTEB(2, 2, 2, 2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: colors.strokeColor,
                width: 1,
              ),
              color: colors.background,
            ),
            child: Center(
              child: Text(
                'Design Team',
                style: textStyles.body3.copyWith(
                  color: colors.primaryText,
                  fontSize: 10,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

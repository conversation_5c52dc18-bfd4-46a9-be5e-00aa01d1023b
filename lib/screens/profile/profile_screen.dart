import 'package:ako_basma/screens/profile/components/account_info/account_info.dart';
import 'package:ako_basma/screens/profile/components/feedback/feedback.dart'
    as manager_feedback;
import 'package:ako_basma/screens/profile/components/performance&achievements/performance.dart';
import 'package:ako_basma/screens/profile/components/settings/settings.dart';
import 'package:ako_basma/screens/profile/components/about/about.dart';
import 'package:ako_basma/screens/profile/components/other/other.dart';
import 'package:ako_basma/screens/home/<USER>/shift_details/top_snackbar.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/components/FAB/ai_floating_action_button.dart';
import 'package:flutter/material.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  // State to control snackbar visibility
  bool _showSnackbar = false;
  String _snackbarMessage = '';

  @override
  Widget build(BuildContext context) {
    final localization = AppLocalizations.of(context)!;

    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      child: accountInfo(context, onEditSuccess: () {
                        // Show success snackbar when edit is successful
                        setState(() {
                          _showSnackbar = true;
                          _snackbarMessage =
                              localization.changesSavedSuccessfully;
                        });

                        // Hide snackbar after 3 seconds
                        Future.delayed(const Duration(seconds: 3), () {
                          if (mounted) {
                            setState(() {
                              _showSnackbar = false;
                            });
                          }
                        });
                      }),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 8),
                      child: const Performance(),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 8),
                      child: const manager_feedback.Feedback(),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 8),
                      child: const Settings(),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 8),
                      child: const About(),
                    ),
                    Container(
                      margin: const EdgeInsetsDirectional.only(top: 8),
                      child: const Other(),
                    ),
                  ],
                ),
              ),
            ),

            // AI Floating Action Button
            const PositionedDirectional(
              bottom: 0,
              end: 0,
              child: AIFloatingActionButton(),
            ),

            // Snackbar overlay (positioned above FAB)
            if (_showSnackbar)
              TopSnackbarWidget(
                message: _snackbarMessage,
                onDismiss: () {
                  setState(() {
                    _showSnackbar = false;
                  });
                },
              ),
          ],
        ),
      ),
    );
  }
}

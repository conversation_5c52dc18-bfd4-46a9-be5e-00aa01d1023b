import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:ako_basma/screens/chat/screen/individual_chat_screen/components/contact_info/contact_info_popup.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

/// Chat header component
/// Displays profile image, name, online status, and last seen time
class ChatHeader extends StatelessWidget {
  final String userName;
  final String userImage;
  final String lastSeen;
  final bool isOnline;
  final VoidCallback onBackPressed;

  const ChatHeader({
    super.key,
    required this.userName,
    required this.userImage,
    required this.lastSeen,
    required this.isOnline,
    required this.onBackPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    final screenWidth = MediaQuery.of(context).size.width;

    return Container(
      width: screenWidth,
      color: colors.backgroundContainer,
      padding: EdgeInsetsDirectional.fromSTEB(
        16,
        MediaQuery.of(context).padding.top + 8,
        16,
        12,
      ),
      child: Row(
        children: [
          // Back button
          Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(20),
              onTap: onBackPressed,
              child: Container(
                padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                child: Icon(
                  DirectionHelpers.getBackArrowIcon(context),
                  size: 32,
                  color: colors.primaryText,
                ),
              ),
            ),
          ),

          // User profile section
          Expanded(
            child: Container(
              margin: const EdgeInsetsDirectional.only(start: 4),
              child: Row(
                children: [
                  // Profile image
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: colors.primaryVariant,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        userImage,
                        width: 40,
                        height: 40,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          // Fallback to initials if image fails to load
                          return Container(
                            color: colors.primary.withOpacity(0.1),
                            child: Center(
                              child: Text(
                                userName.isNotEmpty
                                    ? userName[0].toUpperCase()
                                    : 'U',
                                style: textStyles.headline4.copyWith(
                                  color: colors.primary,
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // User name and status
                  Expanded(
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(start: 12),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // User name - made clickable with GestureDetector
                          GestureDetector(
                            onTap: () => _showContactInfo(context),
                            child: Text(
                              userName,
                              style: textStyles.headline4.copyWith(
                                color: colors.primaryText,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),

                          // Last seen or online status
                          Container(
                            margin: const EdgeInsetsDirectional.only(top: 8),
                            child: Text(
                              isOnline
                                  ? localization.online
                                  : 'Last Seen on 12:00 PM',
                              style: textStyles.body3.copyWith(
                                color: colors.secondaryText,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Show contact info popup as bottom modal sheet
  void _showContactInfo(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      useRootNavigator: true,
      builder: (context) => ContactInfoPopup(
        userName: userName,
        userImage: userImage,
        userEmail:
            '<EMAIL>', // Sample email - replace with actual data
        userPhone:
            '+964 ************', // Sample phone - replace with actual data
        onCall: () {
          // Handle call action
          Navigator.pop(context);
          print('Call pressed for $userName');
        },
        onChat: () {
          // Handle chat action
          Navigator.pop(context);
          print('Chat pressed for $userName');
        },
        onEmail: () {
          // Handle email action
          Navigator.pop(context);
          print('Email pressed for $userName');
        },
      ),
    );
  }
}

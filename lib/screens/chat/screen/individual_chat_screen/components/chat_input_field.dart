import 'package:flutter/material.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

/// Chat input field component for sending messages
/// Includes text input, attachment, and voice message buttons
class ChatInputField extends StatefulWidget {
  final Function(String) onSendMessage;
  final VoidCallback onAttachmentPressed;
  final VoidCallback onVoicePressed;

  const ChatInputField({
    super.key,
    required this.onSendMessage,
    required this.onAttachmentPressed,
    required this.onVoicePressed,
  });

  @override
  State<ChatInputField> createState() => _ChatInputFieldState();
}

class _ChatInputFieldState extends State<ChatInputField> {
  final TextEditingController _messageController = TextEditingController();
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _messageController.addListener(() {
      setState(() {
        _hasText = _messageController.text.trim().isNotEmpty;
      });
    });
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty) {
      widget.onSendMessage(message);
      _messageController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Container(
      width: MediaQuery.of(context).size.width *
          0.96, // Slightly less width than other components
      decoration: BoxDecoration(
        color: colors.background,
      ),

      padding: const EdgeInsetsDirectional.fromSTEB(16, 18, 16, 18),
      child: SafeArea(
        top: false,
        child: Container(
          constraints: const BoxConstraints(
            minHeight: 50,
          ),
          decoration: BoxDecoration(
            color: colors.backgroundContainer,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colors.strokeColor,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Attachment button inside container
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: widget.onAttachmentPressed,
                  child: Container(
                    padding: const EdgeInsetsDirectional.all(16),
                    child: Icon(
                      SolarIconsOutline.paperclip,
                      size: 24,
                      color: colors.tertiaryText,
                    ),
                  ),
                ),
              ),

              // Text field
              Expanded(
                child: Container(
                  padding: const EdgeInsetsDirectional.fromSTEB(
                    8,
                    22, // Increased top padding
                    8,
                    22, // Increased bottom padding
                  ),
                  child: TextField(
                    controller: _messageController,
                    style: textStyles.body2.copyWith(
                      color: colors.primaryText,
                    ),
                    decoration: InputDecoration(
                      hintText: localization.writeAMessage,
                      hintStyle: textStyles.body2.copyWith(
                        color: colors.tertiaryText,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    maxLines: 4,
                    minLines: 1,
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
              ),

              // Send button (shown when there's text) or voice button inside container
              Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(25),
                  onTap: _hasText ? _sendMessage : widget.onVoicePressed,
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: _hasText
                        ? Icon(
                            SolarIconsOutline.plain,
                            size: 24,
                            color: colors.primary,
                          )
                        : Icon(
                            Iconsax.microphone_2_copy,
                            size: 24,
                            color: colors.primary,
                          ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

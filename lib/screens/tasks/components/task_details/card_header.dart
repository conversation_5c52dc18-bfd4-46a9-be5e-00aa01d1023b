import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:iconsax_flutter/iconsax_flutter.dart';
import 'package:solar_icons/solar_icons.dart';

class CardHeader extends StatelessWidget {
  final String taskId;
  const CardHeader({super.key, this.taskId = '#UI007'});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: const EdgeInsetsDirectional.fromSTEB(16, 10, 16, 10),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              taskId,
              style: textStyles.headline3.copyWith(
                color: colors.secondaryText,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Icon(
                SolarIconsOutline.share,
                size: 24,
                color: colors.secondaryText,
              ),
              Container(
                  margin: const EdgeInsetsDirectional.fromSTEB(12, 0, 12, 0)),
              Icon(
                Iconsax.edit_2_copy,
                size: 24,
                color: colors.secondaryText,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

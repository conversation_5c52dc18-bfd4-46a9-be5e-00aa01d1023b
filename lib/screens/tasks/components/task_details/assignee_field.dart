import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class AssigneeField extends StatelessWidget {
  final String label;
  final bool showLabel;

  const AssigneeField({
    super.key,
    this.label = '',
    required this.showLabel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Row(
      children: [
        Expanded(
          flex: 2,
          child: showLabel
              ? Text(
                  label.isNotEmpty ? label : localization.assignee,
                  style: textStyles.body.copyWith(
                    color: colors.tertiaryText,
                  ),
                )
              : Container(),
        ),
        Expanded(
          flex: 3,
          child: Container(
            height: 40,
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 12),
            decoration: BoxDecoration(
              color: colors.background,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: colors.strokeColor, width: 1),
            ),
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: const BoxDecoration(shape: BoxShape.circle),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/images/person.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Container(margin: const EdgeInsetsDirectional.only(start: 8)),
                Text(
                  'Moh Reed',
                  style: textStyles.body2.copyWith(
                    color: colors.secondaryText,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

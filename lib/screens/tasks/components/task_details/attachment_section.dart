import 'dart:io';
import 'package:flutter/material.dart';
import 'package:ako_basma/styles/colors.dart';
import 'package:ako_basma/styles/theme.dart';
import 'package:file_picker/file_picker.dart';
import 'package:solar_icons/solar_icons.dart';
import 'package:ako_basma/l10n/generated/app_localizations.dart';

class AttachmentSection extends StatefulWidget {
  const AttachmentSection({super.key});

  @override
  State<AttachmentSection> createState() => _AttachmentSectionState();
}

class _AttachmentSectionState extends State<AttachmentSection> {
  File? selectedFile;

  /// File picker function
  /// Supports jpg, pdf, png, jpeg formats
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'png', 'jpeg'],
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
        });
        print("File selected: ${result.files.single.path}");

        // Show success feedback to user
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File attached: ${result.files.single.name}'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        print("No file selected or file path is null");
      }
    } catch (e) {
      print("Error picking file: $e");
      // Show error feedback to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error selecting file. Please try again.'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Container(
          margin: const EdgeInsetsDirectional.fromSTEB(16, 8, 16, 8),
          child: Text(
            localization.attachment,
            style: textStyles.body2.copyWith(
              color: colors.secondaryText,
            ),
          ),
        ),

        // File attachment area with gesture detector
        Container(
          margin: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 20),
          child: GestureDetector(
            onTap: _pickFile,
            child: Container(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              decoration: BoxDecoration(
                color: colors.backgroundContainer,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colors.strokeColor, width: 1),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Upload icon container with enhanced styling
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: selectedFile != null
                          ? colors.primary.withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      // border: Border.all(
                      //   color: selectedFile != null
                      //       ? colors.primary
                      //       : colors.strokeColor,
                      //   width: 1,
                      // ),
                    ),
                    child: Icon(
                      selectedFile != null
                          ? SolarIconsOutline.checkCircle
                          : SolarIconsOutline.upload,
                      color: selectedFile != null
                          ? colors.primary
                          : colors.secondaryText,
                      size: 24,
                    ),
                  ),

                  // Spacing between icon and text
                  Container(height: 8),

                  // Dynamic text based on selection state
                  Text(
                    selectedFile != null
                        ? '${localization.fileSelected}: ${selectedFile!.path.split('/').last}'
                        : localization.otherDocuments,
                    style: textStyles.body2.copyWith(
                      color: selectedFile != null
                          ? colors.primary
                          : colors.secondaryText,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

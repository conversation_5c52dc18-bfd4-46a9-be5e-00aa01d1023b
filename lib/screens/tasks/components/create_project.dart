import 'dart:io';

import 'package:ako_basma/l10n/generated/app_localizations.dart';

import 'package:ako_basma/styles/theme.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ako_basma/util/ui/direction_helpers.dart';

class CreateProject extends StatefulWidget {
  final VoidCallback? onBack;
  final VoidCallback? onProjectCreated;

  const CreateProject({
    super.key,
    this.onBack,
    this.onProjectCreated,
  });

  @override
  State<CreateProject> createState() => _CreateProjectState();
}

class _CreateProjectState extends State<CreateProject> {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  File? selectedFile;

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  // file picker extensions
  Future<void> _pickFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['jpg', 'pdf', 'png', 'jpeg'],
      );

      if (result != null &&
          result.files.isNotEmpty &&
          result.files.single.path != null) {
        setState(() {
          selectedFile = File(result.files.single.path!);
        });
        print("File selected: ${result.files.single.path}");
      } else {
        print("No file selected or file path is null");
      }
    } catch (e) {
      print("Error picking file: $e");
    }
  }

  void _handleAddProject() {
    // Close and inform caller that a project has been created
    Navigator.pop(context, true);
    // Additional callbacks (if any) handled by caller via the returned value.
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colors = theme.extension<AppColors>()!;
    final textStyles = theme.extension<TextStyles>()!;
    final localization = AppLocalizations.of(context)!;
    return Scaffold(
      backgroundColor: colors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Header with back button and title
            Padding(
              padding: const EdgeInsetsDirectional.fromSTEB(16, 16, 16, 16),
              child: Row(
                children: [
                  GestureDetector(
                    onTap: widget.onBack ?? () => Navigator.pop(context),
                    child: Container(
                      padding: const EdgeInsetsDirectional.fromSTEB(4, 4, 4, 4),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        DirectionHelpers.getBackArrowIcon(context),
                        color: colors.primaryText,
                        size: 32,
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 12,
                  ),
                  Text(
                    localization.createProject,
                    style: textStyles.headline3.copyWith(
                      color: colors.secondaryText,
                    ),
                  ),
                ],
              ),
            ),

            // Form content
            Expanded(
              child: Padding(
                padding: const EdgeInsetsDirectional.fromSTEB(16, 0, 16, 0),
                child: Column(
                  children: [
                    // Title field
                    Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: titleController,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsetsDirectional.fromSTEB(
                              16, 12, 16, 12),
                          border: InputBorder.none,
                          hintText: localization.title,
                          hintStyle: textStyles.body2.copyWith(
                            color: colors.tertiaryText,
                          ),
                        ),
                        style: textStyles.body.copyWith(
                          color: colors.primaryText,
                        ),
                      ),
                    ),

                    // Description field
                    Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: colors.backgroundContainer,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colors.strokeColor,
                          width: 1,
                        ),
                      ),
                      child: TextField(
                        controller: descriptionController,
                        decoration: InputDecoration(
                          contentPadding: const EdgeInsetsDirectional.fromSTEB(
                              16, 12, 16, 12),
                          border: InputBorder.none,
                          hintText: localization.description,
                          hintStyle: textStyles.body2.copyWith(
                            color: colors.tertiaryText,
                          ),
                        ),
                        style: textStyles.body.copyWith(
                          color: colors.primaryText,
                        ),
                      ),
                    ),

                    // File upload area
                    InkWell(
                      onTap: _pickFile,
                      child: DottedBorder(
                        borderType: BorderType.RRect,
                        radius: const Radius.circular(8),
                        color: colors.strokeColor,
                        dashPattern: const [8, 6],
                        strokeWidth: 1.5,
                        child: Container(
                          decoration: BoxDecoration(
                            color: colors.backgroundContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsetsDirectional.fromSTEB(
                              24, 24, 24, 24),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 48,
                                height: 48,
                                decoration: BoxDecoration(
                                  color: colors.background,
                                  shape: BoxShape.circle,
                                ),
                                child: Center(
                                  child: SvgPicture.asset(
                                    'assets/icons/workspace_screen/upload.svg',
                                    colorFilter: ColorFilter.mode(
                                      colors.primary,
                                      BlendMode.srcIn,
                                    ),
                                    width: 24,
                                    height: 24,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 14),
                              Text(
                                localization.clickToUpload,
                                style: textStyles.body2.copyWith(
                                  color: colors.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Container(height: 14),
                              Text(
                                localization.maxFileSizeInMB(25),
                                style: textStyles.body3.copyWith(
                                  color: colors.secondaryText,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Add button
                    Container(
                      margin: const EdgeInsetsDirectional.only(bottom: 16),
                      width: double.infinity,
                      height: 60,
                      child: ElevatedButton(
                        onPressed: _handleAddProject,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colors.primary,
                          foregroundColor: theme.colorScheme.onPrimary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(16),
                          ),
                        ),
                        child: Text(
                          localization.add,
                          style: textStyles.buttonMedium.copyWith(
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
